import React, { useRef, useEffect, useState } from "react";
import {
  AbsoluteFill,
  Video,
  Audio,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  staticFile,
  Sequence,
  spring,
  Img,
  Easing,
  useVideoMetadata,
} from "remotion";

interface ViralReelProfessionalUltimateProps {
  videoPath: string;
  audioPath: string;
  startTime: number;
  duration: number;
  screenRecordingPath?: string; // Para gravação de tela
}

// Sistema de análise avançada com momentos precisos do podcast
const professionalSpeechSegments = [
  {
    id: "intro",
    text: "Como construir seu primeiro Agente IA",
    startTime: 0,
    endTime: 4.2,
    type: "introduction",
    intensity: "high",
    keywords: ["agente", "IA", "construir"],
    broll: "ai-concept",
    screenFocus: true,
    effects: ["subtle-glow"],
    priority: "high"
  },
  {
    id: "timeline",
    text: "do ZERO em 90 dias",
    startTime: 4.2,
    endTime: 7.8,
    type: "promise",
    intensity: "extreme",
    keywords: ["zero", "90 dias", "prazo"],
    broll: "timeline",
    screenFocus: false,
    effects: ["emphasis", "scale"],
    priority: "viral"
  },
  {
    id: "method",
    text: "Vou te mostrar o passo a passo completo",
    startTime: 7.8,
    endTime: 12.1,
    type: "methodology",
    intensity: "medium",
    keywords: ["passo a passo", "método", "completo"],
    broll: "strategy",
    screenFocus: true,
    effects: ["smooth-transition"],
    priority: "medium"
  },
  {
    id: "value-prop",
    text: "Para transformar sua carreira com IA",
    startTime: 12.1,
    endTime: 16.8,
    type: "value-proposition",
    intensity: "high",
    keywords: ["carreira", "transformar", "IA"],
    broll: "success",
    screenFocus: false,
    effects: ["professional-highlight"],
    priority: "high"
  },
  {
    id: "foundation",
    text: "Primeiro, você precisa entender os fundamentos",
    startTime: 16.8,
    endTime: 21.5,
    type: "education",
    intensity: "medium",
    keywords: ["fundamentos", "entender", "base"],
    broll: "learning",
    screenFocus: true,
    effects: ["knowledge-glow"],
    priority: "medium"
  },
  {
    id: "tools",
    text: "Depois, escolher as ferramentas certas",
    startTime: 21.5,
    endTime: 26.2,
    type: "tools",
    intensity: "high",
    keywords: ["ferramentas", "escolher", "certas"],
    broll: "tools",
    screenFocus: true,
    effects: ["tool-highlight"],
    priority: "high"
  },
  {
    id: "practice",
    text: "E finalmente, colocar em prática",
    startTime: 26.2,
    endTime: 30.5,
    type: "action",
    intensity: "extreme",
    keywords: ["prática", "ação", "implementar"],
    broll: "implementation",
    screenFocus: true,
    effects: ["action-emphasis"],
    priority: "viral"
  },
  {
    id: "secret",
    text: "O segredo está na consistência",
    startTime: 30.5,
    endTime: 34.8,
    type: "insight",
    intensity: "extreme",
    keywords: ["segredo", "consistência"],
    broll: "consistency",
    screenFocus: false,
    effects: ["secret-reveal"],
    priority: "viral"
  },
  {
    id: "proven",
    text: "E em seguir um método comprovado",
    startTime: 34.8,
    endTime: 39.1,
    type: "proof",
    intensity: "high",
    keywords: ["método", "comprovado", "validado"],
    broll: "validation",
    screenFocus: false,
    effects: ["proof-badge"],
    priority: "high"
  },
  {
    id: "social-proof",
    text: "Que já funcionou para milhares de pessoas",
    startTime: 39.1,
    endTime: 43.8,
    type: "social-proof",
    intensity: "high",
    keywords: ["milhares", "pessoas", "funcionou"],
    broll: "community",
    screenFocus: false,
    effects: ["social-validation"],
    priority: "high"
  },
  {
    id: "cta-potential",
    text: "Você pode ser o próximo!",
    startTime: 43.8,
    endTime: 47.2,
    type: "call-to-action",
    intensity: "extreme",
    keywords: ["próximo", "você pode"],
    broll: "success-individual",
    screenFocus: false,
    effects: ["cta-highlight", "pulse"],
    priority: "viral"
  },
  {
    id: "requirements",
    text: "Basta ter dedicação e foco",
    startTime: 47.2,
    endTime: 51.5,
    type: "requirements",
    intensity: "medium",
    keywords: ["dedicação", "foco", "requisitos"],
    broll: "dedication",
    screenFocus: false,
    effects: ["requirement-list"],
    priority: "medium"
  }
];

// Sistema de B-roll contextual profissional
const contextualBrollAssets = {
  "ai-concept": "broll/ai-visualization.jpg",
  "timeline": "broll/strategy-planning.jpg",
  "strategy": "broll/strategy-planning.jpg",
  "success": "broll/success-celebration.jpg",
  "learning": "broll/ai-visualization.jpg",
  "tools": "broll/ai-visualization.jpg",
  "implementation": "broll/handshake-professional.jpg",
  "consistency": "broll/strategy-planning.jpg",
  "validation": "broll/contract-signing.jpg",
  "community": "broll/success-celebration.jpg",
  "success-individual": "broll/success-celebration.jpg",
  "dedication": "broll/strategy-planning.jpg"
};

export const ViralReelProfessionalUltimate: React.FC<ViralReelProfessionalUltimateProps> = ({
  videoPath,
  audioPath,
  startTime,
  duration,
  screenRecordingPath = "temp_video_base.mp4" // Fallback para demo
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps, width, height } = useVideoConfig();
  const time = frame / fps;

  // Sistema de análise de segmento atual
  const getCurrentSegment = () => {
    return professionalSpeechSegments.find(
      segment => time >= segment.startTime && time < segment.endTime
    );
  };

  const currentSegment = getCurrentSegment();
  const nextSegment = professionalSpeechSegments.find(
    segment => segment.startTime > time
  );

  // Sistema de transições suaves entre segmentos
  const getTransitionProgress = () => {
    if (!currentSegment) return 0;
    
    const segmentProgress = (time - currentSegment.startTime) / 
                           (currentSegment.endTime - currentSegment.startTime);
    
    return segmentProgress;
  };

  // Sistema de intensidade profissional
  const getIntensityConfig = (intensity: string) => {
    const configs = {
      "extreme": { scale: 1.02, opacity: 1, glow: 0.8 },
      "high": { scale: 1.01, opacity: 0.95, glow: 0.6 },
      "medium": { scale: 1.005, opacity: 0.9, glow: 0.4 },
      "low": { scale: 1, opacity: 0.85, glow: 0.2 }
    };
    
    return configs[intensity as keyof typeof configs] || configs.medium;
  };

  const intensityConfig = currentSegment ? 
    getIntensityConfig(currentSegment.intensity) : 
    { scale: 1, opacity: 0.9, glow: 0.3 };

  // Sistema de efeitos sutis e profissionais
  const getSubtleEffects = () => {
    if (!currentSegment?.effects) return {};

    const effects = currentSegment.effects;
    const progress = getTransitionProgress();

    let transformEffects = {
      scale: intensityConfig.scale,
      brightness: 1,
      contrast: 1,
      saturation: 1
    };

    // Efeitos sutis baseados no tipo
    effects.forEach(effect => {
      switch(effect) {
        case "subtle-glow":
          transformEffects.brightness = 1 + (0.05 * Math.sin(time * 2));
          break;
        case "emphasis":
          transformEffects.scale = 1 + (0.01 * Math.sin(time * 4));
          break;
        case "professional-highlight":
          transformEffects.contrast = 1 + (0.05 * Math.sin(time * 1.5));
          break;
        case "knowledge-glow":
          transformEffects.saturation = 1 + (0.1 * Math.sin(time * 3));
          break;
      }
    });

    return transformEffects;
  };

  const effects = getSubtleEffects();

  // Sistema de timing para B-roll estratégico
  const shouldShowBroll = () => {
    return currentSegment?.broll && 
           !currentSegment.screenFocus && 
           currentSegment.priority === "viral";
  };

  // Animação de entrada suave para elementos
  const getEntryAnimation = (delay: number = 0) => {
    return spring({
      frame: Math.max(0, frame - delay),
      fps,
      config: { damping: 200, stiffness: 100 }
    });
  };

  return (
    <AbsoluteFill style={{ backgroundColor: "#000" }}>
      {/* Áudio sincronizado */}
      <Audio
        src={staticFile(audioPath)}
        startFrom={0}
        endAt={duration * fps}
        volume={1.0}
      />

      {/* Layout Split-Screen Profissional */}
      <div style={{
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column"
      }}>
        
        {/* METADE SUPERIOR - Pessoa/Instrutor */}
        <div style={{
          height: "50%",
          width: "100%",
          position: "relative",
          overflow: "hidden",
          background: "linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)"
        }}>
          <Video
            src={staticFile(videoPath)}
            startFrom={startTime * fps}
            endAt={(startTime + duration) * fps}
            style={{
              width: "100%",
              height: "120%", // Crop estratégico
              objectFit: "cover",
              objectPosition: "center top",
              transform: `scale(${effects.scale})`,
              filter: `
                brightness(${effects.brightness}) 
                contrast(${effects.contrast}) 
                saturate(${effects.saturation})
              `,
              transition: "all 0.3s ease"
            }}
            muted={true}
          />

          {/* Overlay sutil para profissionalismo */}
          <div style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.1) 100%)",
            pointerEvents: "none"
          }} />

          {/* B-roll contextual estratégico */}
          {shouldShowBroll() && currentSegment?.broll && (
            <BrollOverlay
              asset={contextualBrollAssets[currentSegment.broll]}
              intensity={intensityConfig.glow}
              transitionProgress={getTransitionProgress()}
            />
          )}
        </div>

        {/* METADE INFERIOR - Screen Recording/Ferramenta */}
        <div style={{
          height: "50%",
          width: "100%",
          position: "relative",
          overflow: "hidden",
          background: "#0f0f0f"
        }}>
          <Video
            src={staticFile(screenRecordingPath)}
            startFrom={startTime * fps}
            endAt={(startTime + duration) * fps}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              filter: "brightness(1.1) contrast(1.05)"
            }}
            muted={true}
          />

          {/* Indicador de foco quando necessário */}
          {currentSegment?.screenFocus && (
            <div style={{
              position: "absolute",
              top: 10,
              right: 10,
              background: "rgba(34, 197, 94, 0.9)",
              padding: "4px 12px",
              borderRadius: "12px",
              fontSize: "12px",
              fontWeight: "600",
              color: "#fff",
              backdropFilter: "blur(10px)",
              opacity: getEntryAnimation(10)
            }}>
              🎯 FOCO
            </div>
          )}
        </div>
      </div>

      {/* Sistema de Legendas Profissionais Minimalistas */}
      {currentSegment && (
        <ProfessionalSubtitleSystem
          segment={currentSegment}
          progress={getTransitionProgress()}
          effects={effects}
        />
      )}

      {/* Indicadores profissionais discretos */}
      <ProfessionalIndicators
        currentSegment={currentSegment}
        nextSegment={nextSegment}
        progress={time / duration}
        totalSegments={professionalSpeechSegments.length}
      />

      {/* Separador visual entre as seções */}
      <div style={{
        position: "absolute",
        top: "50%",
        left: 0,
        right: 0,
        height: "2px",
        background: "linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%)",
        transform: "translateY(-1px)"
      }} />
    </AbsoluteFill>
  );
};

// Componente de B-roll contextual
const BrollOverlay: React.FC<{
  asset: string;
  intensity: number;
  transitionProgress: number;
}> = ({ asset, intensity, transitionProgress }) => {
  const opacity = interpolate(
    transitionProgress,
    [0, 0.2, 0.8, 1],
    [0, intensity * 0.6, intensity * 0.6, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  return (
    <div style={{
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      opacity,
      zIndex: 5
    }}>
      <Img
        src={staticFile(asset)}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
          filter: "brightness(0.8) contrast(1.2)"
        }}
      />
      <div style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: "rgba(0,0,0,0.3)",
        mixBlendMode: "overlay"
      }} />
    </div>
  );
};

// Sistema de legendas profissionais
const ProfessionalSubtitleSystem: React.FC<{
  segment: any;
  progress: number;
  effects: any;
}> = ({ segment, progress, effects }) => {
  const entryAnimation = spring({
    frame: progress * 30,
    fps: 30,
    config: { damping: 150, stiffness: 200 }
  });

  const isHighPriority = segment.priority === "viral";

  return (
    <div style={{
      position: "absolute",
      bottom: isHighPriority ? "55%" : "60%", // Posicionamento estratégico
      left: "5%",
      right: "5%",
      zIndex: 100,
      opacity: entryAnimation,
      transform: `translateY(${(1 - entryAnimation) * 20}px)`
    }}>
      <div style={{
        background: isHighPriority 
          ? "linear-gradient(135deg, rgba(34, 197, 94, 0.95), rgba(22, 163, 74, 0.95))"
          : "rgba(0, 0, 0, 0.85)",
        backdropFilter: "blur(20px)",
        border: isHighPriority 
          ? "2px solid rgba(34, 197, 94, 0.5)"
          : "1px solid rgba(255, 255, 255, 0.1)",
        borderRadius: "16px",
        padding: isHighPriority ? "16px 24px" : "12px 20px",
        boxShadow: isHighPriority
          ? "0 8px 32px rgba(34, 197, 94, 0.3)"
          : "0 8px 32px rgba(0, 0, 0, 0.4)"
      }}>
        <div style={{
          fontSize: isHighPriority ? "20px" : "18px",
          fontWeight: isHighPriority ? "700" : "600",
          color: "#ffffff",
          textAlign: "center",
          lineHeight: "1.4",
          fontFamily: "'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif",
          textShadow: "0 2px 8px rgba(0,0,0,0.5)"
        }}>
          {segment.text}
        </div>

        {/* Indicador de prioridade sutil */}
        {isHighPriority && (
          <div style={{
            position: "absolute",
            top: "-8px",
            right: "16px",
            background: "rgba(34, 197, 94, 1)",
            padding: "2px 8px",
            borderRadius: "8px",
            fontSize: "10px",
            fontWeight: "600",
            color: "#fff",
            textTransform: "uppercase",
            letterSpacing: "0.5px"
          }}>
            VIRAL
          </div>
        )}
      </div>
    </div>
  );
};

// Indicadores profissionais discretos
const ProfessionalIndicators: React.FC<{
  currentSegment: any;
  nextSegment: any;
  progress: number;
  totalSegments: number;
}> = ({ currentSegment, nextSegment, progress, totalSegments }) => {
  return (
    <>
      {/* Progress bar minimalista */}
      <div style={{
        position: "absolute",
        bottom: "8px",
        left: "20px",
        right: "20px",
        height: "2px",
        background: "rgba(255, 255, 255, 0.1)",
        borderRadius: "1px",
        overflow: "hidden"
      }}>
        <div style={{
          height: "100%",
          background: currentSegment?.priority === "viral"
            ? "linear-gradient(90deg, #22c55e, #16a34a)"
            : "linear-gradient(90deg, #6b7280, #4b5563)",
          width: `${progress * 100}%`,
          borderRadius: "1px",
          transition: "width 0.1s ease"
        }} />
      </div>

      {/* Contador de segmentos discreto */}
      <div style={{
        position: "absolute",
        top: "20px",
        right: "20px",
        background: "rgba(0, 0, 0, 0.6)",
        backdropFilter: "blur(10px)",
        padding: "6px 12px",
        borderRadius: "12px",
        border: "1px solid rgba(255, 255, 255, 0.1)"
      }}>
        <span style={{
          fontSize: "12px",
          fontWeight: "600",
          color: "#fff",
          fontFamily: "'SF Pro Display', sans-serif"
        }}>
          {currentSegment ? 
            professionalSpeechSegments.findIndex(s => s.id === currentSegment.id) + 1 : 1
          } / {totalSegments}
        </span>
      </div>

      {/* Próximo segmento preview */}
      {nextSegment && (
        <div style={{
          position: "absolute",
          bottom: "40px",
          right: "20px",
          background: "rgba(0, 0, 0, 0.7)",
          backdropFilter: "blur(15px)",
          padding: "8px 12px",
          borderRadius: "12px",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          maxWidth: "200px"
        }}>
          <div style={{
            fontSize: "10px",
            color: "rgba(255, 255, 255, 0.6)",
            marginBottom: "2px",
            textTransform: "uppercase",
            letterSpacing: "0.5px"
          }}>
            Próximo
          </div>
          <div style={{
            fontSize: "12px",
            color: "#fff",
            fontWeight: "500",
            lineHeight: "1.3"
          }}>
            {nextSegment.text.substring(0, 40)}...
          </div>
        </div>
      )}
    </>
  );
};
