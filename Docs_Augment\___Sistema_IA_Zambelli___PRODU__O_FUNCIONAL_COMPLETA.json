{"name": "🚀 Sistema IA Zambelli - PRODUÇÃO FUNCIONAL COMPLETA", "nodes": [{"parameters": {"assignments": {"assignments": [{"name": "telefone_limpo", "value": "={{ $json.body.data.key.remoteJid.split('@')[0].replace(/\\D/g, '') }}", "type": "string"}, {"name": "mensagem", "value": "={{ $json.body.data.message.conversation || $json.body.data.message.extendedTextMessage?.text || '' }}", "type": "string"}, {"name": "nome_cliente", "value": "={{ $json.body.data.pushName || 'Cliente' }}", "type": "string"}, {"name": "from_me", "value": "={{ $json.body.data.key.fromMe }}", "type": "boolean"}, {"name": "instance_name", "value": "={{ $json.body.instance }}", "type": "string"}, {"name": "timestamp", "value": "={{ $now.format('yyyy-MM-dd HH:mm:ss') }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, 300], "id": "tratamento-dados", "name": "🔧 Tratamento de Dados"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $json.from_me }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Empresa"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $json.from_me }}", "rightValue": false, "operator": {"type": "boolean", "operation": "false", "singleValue": true}}]}, "renameOutput": true, "outputKey": "Cliente"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [500, 300], "id": "switch-fromme", "name": "🔀 Origem: <PERSON>p<PERSON>a ou Cliente?"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1ax_n4TGBrXJlDratKfylDx4RDYHLuKryFvXZ2ML9OIU", "mode": "id"}, "sheetName": {"__rl": true, "value": "Leads", "mode": "name"}, "columns": {"mappingMode": "defineBelow", "value": {"Mensagem Atendente": "={{ $json.mensagem }}", "WhatsApp": "={{ $json.telefone_limpo }}"}, "matchingColumns": ["WhatsApp"], "schema": [{"id": "WhatsApp", "displayName": "WhatsApp", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Nome", "displayName": "Nome", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Número Processo", "displayName": "Número Processo", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "<PERSON><PERSON>", "displayName": "<PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Mensagem IA", "displayName": "Mensagem IA", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Enviada?", "displayName": "Enviada?", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Respondeu?", "displayName": "Respondeu?", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Mensagem Cliente", "displayName": "Mensagem Cliente", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Mensagem Atendente", "displayName": "Mensagem Atendente", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Classificação IA de Inteção", "displayName": "Classificação IA de Inteção", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Resposta IA", "displayName": "Resposta IA", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Valor Proposta", "displayName": "Valor Proposta", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Score Confiança", "displayName": "Score Confiança", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Timestamp IA", "displayName": "Timestamp IA", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [700, 200], "id": "update-empresa", "name": "📊 Registrar Mensagem Empresa", "credentials": {"googleSheetsOAuth2Api": {"id": "EyZuOH6xOjRAaRRG", "name": "Google Sheets Monteleone.w1"}}}, {"parameters": {"inputText": "={{ $('🔧 Tratamento de Dados').item.json.mensagem }}", "categories": {"categories": [{"category": "INTERESSE_ALTO", "description": "Cliente demonstra interesse direto: 'sim', 'quero saber', 'qual seria a oferta', 'me interessei', 'gostaria de receber', 'aceito', 'quero proposta', 'vamos fazer', 'concordo'"}, {"category": "INTERESSE_MEDIO", "description": "Cliente faz perguntas: 'como funciona', 'quais documentos', 'quanto tempo', 'é seguro', 'explique melhor', 'preciso de mais informações', 'qual seria a proposta'"}, {"category": "INTERESSE_BAIXO", "description": "Respostas evasivas: 'talvez', 'vou pensar', 'depois', 'não sei', 'vou ver'"}, {"category": "OBJECAO", "description": "Resistência clara: 'não tenho interesse', 'não quero', 'é golpe', 'não confio', 'já tenho advogado', 'parece golpe', 'não acredito'"}, {"category": "IRRELEVANTE", "description": "Fora do contexto: perguntas não relacionadas a precatórios, 'oi', 'olá', 'tudo bem'"}, {"category": "ENCAMINHAR_HUMANO", "description": "Complexidade alta: 'quero falar com alguém', 'vocês tem escritório', dúvidas técnicas específicas, casos especiais, documentação complexa, 'preciso de ajuda'"}]}, "options": {"systemPromptTemplate": "Você é um especialista em análise de intenção para ofertas de antecipação de precatórios da Zambelli Money. Analise a mensagem do cliente e classifique precisamente a intenção. Considere o contexto de precatórios e antecipação financeira."}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1.1, "position": [1080, 400], "id": "classificador-ia", "name": "🤖 Classificador IA - Intenção"}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {"temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1060, 600], "id": "gemini-model-classificador", "name": "🧠 Gemini - Classificador", "credentials": {"googlePalmApi": {"id": "VaXlSvho265r61wZ", "name": "Google Gemini API"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1ax_n4TGBrXJlDratKfylDx4RDYHLuKryFvXZ2ML9OIU", "mode": "id"}, "sheetName": {"__rl": true, "value": "<PERSON><PERSON>", "mode": "name"}, "filtersUI": {"values": [{"lookupColumn": "Nome", "lookupValue": "={{ $json.Nome }}"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [900, 400], "id": "buscar-dados", "name": "📋 Buscar Dados Cliente", "credentials": {"googleSheetsOAuth2Api": {"id": "EyZuOH6xOjRAaRRG", "name": "Google Sheets Monteleone.w1"}}}, {"parameters": {"promptType": "define", "text": "=Cliente demonstrou interesse médio em antecipação de precatórios. Responda de forma qualificativa e educativa:\\n\\n**MENSAGEM DO CLIENTE:** {{ $('🔧 Tratamento de Dados').item.json.mensagem }}\\n**NOME:** {{ $('🔧 Tratamento de Dados').item.json.nome_cliente }}\\n\\nResponda de forma empática, tire dúvidas e desperte interesse para a proposta.", "options": {"systemMessage": "=Você é <PERSON>, consultor especialista da Zambelli Money em antecipação de precatórios. Sua missão é qualificar leads com interesse médio através de perguntas estratégicas e educação sobre o processo.\\n\\nDIRETRIZES:\\n- Seja empático e profissional\\n- Explique brevemente como funciona a antecipação\\n- Faça 1-2 perguntas qualificativas\\n- Desperte interesse sem pressionar\\n- Máximo 2 parágrafos\\n- Use linguagem acessível\\n- Mencione benefícios: rapidez, segurança, sem burocracia"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1240, 1080], "id": "qualificador-ia", "name": "❓ Qualificador IA - Interesse M<PERSON>dio", "disabled": true}, {"parameters": {"promptType": "define", "text": "=Cliente apresentou objeção sobre antecipação de precatórios. Trate profissionalmente:\\n\\n**MENSAGEM DO CLIENTE:** {{ $('🔧 Tratamento de Dados').item.json.mensagem }}\\n**NOME:** {{ $('🔧 Tratamento de Dados').item.json.nome_cliente }}\\n\\nValide a preocupação e apresente benefícios de forma respeitosa.", "options": {"systemMessage": "=Você é <PERSON>, especialista da Zambelli Money em tratamento de objeções sobre precatórios.\\n\\nDIRETRIZES:\\n- Valide a preocupação do cliente\\n- Apresente credenciais e segurança da empresa\\n- Explique o processo transparente\\n- Ofereça próximo passo sem pressão\\n- Tom empático e respeitoso\\n- Máximo 2 parágrafos\\n- Mencione: regulamentação, transparência, histórico de sucesso\\n- Deixe porta aberta para futuro contato"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1240, 1420], "id": "tratador-objecoes", "name": "🛡️ Tratador de Objeções", "disabled": true}, {"parameters": {"promptType": "define", "text": "=Cliente demonstrou baixo interesse. Responda de forma sutil para manter relacionamento:\\n\\n**MENSAGEM DO CLIENTE:** {{ $('🔧 Tratamento de Dados').item.json.mensagem }}\\n**NOME:** {{ $('🔧 Tratamento de Dados').item.json.nome_cliente }}\\n\\nMantenha a porta aberta para futuro contato.", "options": {"systemMessage": "=Você é Luan <PERSON>ne da Zambelli Money. O cliente demonstrou baixo interesse, mas você quer manter o relacionamento.\\n\\nDIRETRIZES:\\n- Seja respeitoso e não insistente\\n- Deixe informação de contato\\n- Mencione que estará disponível quando precisar\\n- Tom amigável e profissional\\n- Máximo 1 parágrafo\\n- Não pressione\\n- Foque em disponibilidade futura"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1260, 1740], "id": "nurturing-ia", "name": "🌱 Nurturing IA - <PERSON><PERSON><PERSON>", "disabled": true}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {"temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1220, 1260], "id": "2861316f-394e-4a95-8188-d9253d21852e", "name": "🧠 Gemini - Classificador1", "credentials": {"googlePalmApi": {"id": "VaXlSvho265r61wZ", "name": "Google Gemini API"}}, "disabled": true}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {"temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1220, 1580], "id": "74cf373b-acb6-47d1-b199-550dbe639933", "name": "🧠 Gemini - Classificador2", "credentials": {"googlePalmApi": {"id": "VaXlSvho265r61wZ", "name": "Google Gemini API"}}, "disabled": true}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {"temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1220, 1880], "id": "86ab68e6-dc5c-429f-8641-9d082fd99479", "name": "🧠 Gemini - Classificador3", "credentials": {"googlePalmApi": {"id": "VaXlSvho265r61wZ", "name": "Google Gemini API"}}, "disabled": true}, {"parameters": {"httpMethod": "POST", "path": "ia_disparo", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [120, 300], "id": "c628ecf9-453c-4a83-bc9b-2f7f3cf5d051", "name": "🎯 Webhook EVO - Entrada Principal1", "webhookId": "e68d4a98-21ea-4efb-a4df-ea3bd81e819a"}, {"parameters": {"content": "## Modulos IA - Após Qualificação de Intenção", "height": 1040, "width": 520, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [1140, 1020], "typeVersion": 1, "id": "bdd9246e-265e-44dd-ab4b-312d194fbeb4", "name": "<PERSON><PERSON>"}, {"parameters": {"documentId": {"__rl": true, "value": "1ax_n4TGBrXJlDratKfylDx4RDYHLuKryFvXZ2ML9OIU", "mode": "id"}, "sheetName": {"__rl": true, "value": 1076380534, "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ax_n4TGBrXJlDratKfylDx4RDYHLuKryFvXZ2ML9OIU/edit#gid=1076380534"}, "filtersUI": {"values": [{"lookupColumn": "WhatsApp", "lookupValue": "={{ $json.telefone_limpo }}"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [720, 400], "id": "0ae26724-af95-4e9d-aea0-12ad6b9d0a14", "name": "📋 Buscar Lead", "credentials": {"googleSheetsOAuth2Api": {"id": "EyZuOH6xOjRAaRRG", "name": "Google Sheets Monteleone.w1"}}}, {"parameters": {"assignments": {"assignments": [{"name": "nome_cliente", "value": "={{ $('📋 Buscar Dados Cliente').item.json.Nome || $('🔧 Tratamento de Dados').item.json.nome_cliente }}", "type": "string", "id": "43583311-6b13-4153-8eed-8222e9a4d72d"}, {"name": "cpf_cliente", "value": "={{ $('📋 Buscar Dados Cliente').item.json['CPF'] || 'N/A' }}", "type": "string", "id": "5ca17455-cb80-478f-a443-451f6ca81554"}, {"name": "numero_processo", "value": "={{ $('📋 Buscar Dados Cliente').item.json['Nº PROCESSO (Completo)'] || 'N/A' }}", "type": "string", "id": "e4b5b362-e7c9-43a8-ac4f-4ce732b30ede"}, {"name": "tribunal", "value": "={{ $('📋 Buscar Dados Cliente').item.json.Tribunal || 'N/A' }}", "type": "string", "id": "19107cd5-090c-4082-b58b-01781abacbd5"}, {"name": "natureza", "value": "={{ $('📋 Buscar Dados Cliente').item.json.Natureza || 'N/A' }}", "type": "string", "id": "391e1e12-9442-4f10-aaa9-9373aa3f82c9"}, {"name": "valor_original", "value": "={{ $('📋 Buscar Dados Cliente').item.json['VALOR TOTAL'] || 'N/A' }}", "type": "string", "id": "bd6de8d2-a983-447c-87a4-9821082cc2fd"}, {"name": "data_hoje", "value": "={{ $now.format('dd/MM/yyyy') }}", "type": "string", "id": "1064067d-8cac-4371-b6db-33a850c5c8eb"}, {"id": "f845cd1e-937a-4cd3-9b07-b6c35ea38ae6", "name": "mensagem_cliente", "value": "={{ $('🔧 Tratamento de Dados').item.json.mensagem }}", "type": "string"}, {"id": "243dfcde-8228-4044-9af7-90732245f314", "name": "temperatura_cliente", "value": "INTERESSE_MEDIO", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1520, 280], "id": "preparar-pdf", "name": "📝 Preparar Dados Proposta"}, {"parameters": {"assignments": {"assignments": [{"name": "nome_cliente", "value": "={{ $('📋 Buscar Dados Cliente').item.json.Nome || $('🔧 Tratamento de Dados').item.json.nome_cliente }}", "type": "string", "id": "43583311-6b13-4153-8eed-8222e9a4d72d"}, {"name": "cpf_cliente", "value": "={{ $('📋 Buscar Dados Cliente').item.json['CPF'] || 'N/A' }}", "type": "string", "id": "5ca17455-cb80-478f-a443-451f6ca81554"}, {"name": "numero_processo", "value": "={{ $('📋 Buscar Dados Cliente').item.json['Nº PROCESSO (Completo)'] || 'N/A' }}", "type": "string", "id": "e4b5b362-e7c9-43a8-ac4f-4ce732b30ede"}, {"name": "tribunal", "value": "={{ $('📋 Buscar Dados Cliente').item.json.Tribunal || 'N/A' }}", "type": "string", "id": "19107cd5-090c-4082-b58b-01781abacbd5"}, {"name": "natureza", "value": "={{ $('📋 Buscar Dados Cliente').item.json.Natureza || 'N/A' }}", "type": "string", "id": "391e1e12-9442-4f10-aaa9-9373aa3f82c9"}, {"name": "valor_original", "value": "={{ $('📋 Buscar Dados Cliente').item.json['VALOR TOTAL'] || 'N/A' }}", "type": "string", "id": "bd6de8d2-a983-447c-87a4-9821082cc2fd"}, {"name": "data_hoje", "value": "={{ $now.format('dd/MM/yyyy') }}", "type": "string", "id": "1064067d-8cac-4371-b6db-33a850c5c8eb"}, {"id": "f845cd1e-937a-4cd3-9b07-b6c35ea38ae6", "name": "mensagem_cliente", "value": "={{ $('🔧 Tratamento de Dados').item.json.mensagem }}", "type": "string"}, {"id": "243dfcde-8228-4044-9af7-90732245f314", "name": "temperatura_cliente", "value": "INTERESSE_ALTO", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1520, 100], "id": "b5b60d09-0c33-4dae-8bed-50a0eedda1d1", "name": "📝 Preparar Dados Proposta1"}, {"parameters": {"assignments": {"assignments": [{"name": "nome_cliente", "value": "={{ $('📋 Buscar Dados Cliente').item.json.Nome || $('🔧 Tratamento de Dados').item.json.nome_cliente }}", "type": "string", "id": "43583311-6b13-4153-8eed-8222e9a4d72d"}, {"name": "cpf_cliente", "value": "={{ $('📋 Buscar Dados Cliente').item.json['CPF'] || 'N/A' }}", "type": "string", "id": "5ca17455-cb80-478f-a443-451f6ca81554"}, {"name": "numero_processo", "value": "={{ $('📋 Buscar Dados Cliente').item.json['Nº PROCESSO (Completo)'] || 'N/A' }}", "type": "string", "id": "e4b5b362-e7c9-43a8-ac4f-4ce732b30ede"}, {"name": "tribunal", "value": "={{ $('📋 Buscar Dados Cliente').item.json.Tribunal || 'N/A' }}", "type": "string", "id": "19107cd5-090c-4082-b58b-01781abacbd5"}, {"name": "natureza", "value": "={{ $('📋 Buscar Dados Cliente').item.json.Natureza || 'N/A' }}", "type": "string", "id": "391e1e12-9442-4f10-aaa9-9373aa3f82c9"}, {"name": "valor_original", "value": "={{ $('📋 Buscar Dados Cliente').item.json['VALOR TOTAL'] || 'N/A' }}", "type": "string", "id": "bd6de8d2-a983-447c-87a4-9821082cc2fd"}, {"name": "data_hoje", "value": "={{ $now.format('dd/MM/yyyy') }}", "type": "string", "id": "1064067d-8cac-4371-b6db-33a850c5c8eb"}, {"id": "f845cd1e-937a-4cd3-9b07-b6c35ea38ae6", "name": "mensagem_cliente", "value": "={{ $('🔧 Tratamento de Dados').item.json.mensagem }}", "type": "string"}, {"id": "243dfcde-8228-4044-9af7-90732245f314", "name": "temperatura_cliente", "value": "INTERESSE_BAIXO", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1520, 460], "id": "9ac99238-03d4-4f67-a412-1684424565f5", "name": "📝 Preparar Dados Proposta2"}, {"parameters": {"resource": "messages-api", "operation": "send-document", "instanceName": "ia_disparo", "remoteJid": "={{ $('🔧 Tratamento de Dados').item.json.telefone_limpo }}", "media": "data", "caption": "📄 *PROPOSTA PERSONALIZADA ZAMBELLI MONEY*\\n\\n✅ **Valor da Proposta:** {{ $('📝 Preparar Dados PDF').item.json.valor_proposta_formatado }}\\n💰 **Percentual:** 70% do valor total\\n⏰ **Válida até:** {{ $('📝 Preparar Dados PDF').item.json.data_validade }}\\n⚡ **Prazo:** {{ $('📝 Preparar Dados PDF').item.json.prazo_recebimento }}\\n\\n📞 **Consultor:** <PERSON><PERSON>\\n📱 **WhatsApp:** (11) 93905-6750\\n\\n📝 Analise com calma sua proposta e entre em contato para esclarecer dúvidas ou prosseguir com a operação.", "fileName": "={{ $('📋 Gerar PDF Profissional').item.json.fileName }}", "options_message": {"delay": 2000}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [1820, 280], "id": "enviar-pdf", "name": "📤 En<PERSON><PERSON>pp - Consultor", "credentials": {"evolutionApi": {"id": "wADzsLqMuGwHdmYL", "name": "Evolution account"}}}], "pinData": {"🎯 Webhook EVO - Entrada Principal1": [{"json": {"headers": {"host": "webhooks.fulcroalavanc.com.br", "user-agent": "axios/1.7.9", "content-length": "994", "accept-encoding": "gzip, compress, deflate, br", "content-type": "application/json", "x-forwarded-for": "**********", "x-forwarded-host": "webhooks.fulcroalavanc.com.br", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik_traefik.1", "x-real-ip": "**********"}, "params": {}, "query": {}, "body": {"event": "messages.upsert", "instance": "ia_disparo", "data": {"key": {"remoteJid": "<EMAIL>", "fromMe": false, "id": "E2F6D9F33396731B54C484A2B0FC226C"}, "pushName": "<PERSON>", "status": "DELIVERY_ACK", "message": {"messageContextInfo": {"deviceListMetadata": {"recipientKeyHash": "O5k1x7b92mH2Rw==", "recipientTimestamp": "**********"}, "deviceListMetadataVersion": 2, "messageSecret": "tJus9XPHn8UzLJAWYn5IeaqeGG6M84n3YAhQu4HCp3U="}, "conversation": "quais documentos"}, "contextInfo": {"expiration": 7776000, "ephemeralSettingTimestamp": "1695822942", "disappearingMode": {"initiator": "INITIATED_BY_ME", "trigger": "UNKNOWN"}}, "messageType": "conversation", "messageTimestamp": 1752189197, "instanceId": "41c21233-3c2b-4425-9e25-fc550ea6603b", "source": "android"}, "destination": "https://webhooks.fulcroalavanc.com.br/webhook/ia_disparo", "date_time": "2025-07-10T20:13:18.131Z", "sender": "<EMAIL>", "server_url": "https://evosami.fulcroalavanc.com.br", "apikey": "3CAF322E333B-4E8A-BB94-B96841B3F62E"}, "webhookUrl": "https://webhooks.fulcroalavanc.com.br/webhook/ia_disparo", "executionMode": "production"}}]}, "connections": {"🔧 Tratamento de Dados": {"main": [[{"node": "🔀 Origem: <PERSON>p<PERSON>a ou Cliente?", "type": "main", "index": 0}]]}, "🔀 Origem: Empresa ou Cliente?": {"main": [[{"node": "📊 Registrar Mensagem Empresa", "type": "main", "index": 0}], [{"node": "📋 Buscar Lead", "type": "main", "index": 0}]]}, "🧠 Gemini - Classificador": {"ai_languageModel": [[{"node": "🤖 Classificador IA - Intenção", "type": "ai_languageModel", "index": 0}]]}, "🤖 Classificador IA - Intenção": {"main": [[{"node": "📝 Preparar Dados Proposta1", "type": "main", "index": 0}], [{"node": "📝 Preparar Dados Proposta", "type": "main", "index": 0}], [{"node": "📝 Preparar Dados Proposta2", "type": "main", "index": 0}], [], [], []]}, "📋 Buscar Dados Cliente": {"main": [[{"node": "🤖 Classificador IA - Intenção", "type": "main", "index": 0}]]}, "🧠 Gemini - Classificador1": {"ai_languageModel": [[{"node": "❓ Qualificador IA - Interesse M<PERSON>dio", "type": "ai_languageModel", "index": 0}]]}, "🧠 Gemini - Classificador2": {"ai_languageModel": [[{"node": "🛡️ Tratador de Objeções", "type": "ai_languageModel", "index": 0}]]}, "🧠 Gemini - Classificador3": {"ai_languageModel": [[{"node": "🌱 Nurturing IA - <PERSON><PERSON><PERSON>", "type": "ai_languageModel", "index": 0}]]}, "🎯 Webhook EVO - Entrada Principal1": {"main": [[{"node": "🔧 Tratamento de Dados", "type": "main", "index": 0}]]}, "📋 Buscar Lead": {"main": [[{"node": "📋 Buscar Dados Cliente", "type": "main", "index": 0}]]}, "📝 Preparar Dados Proposta1": {"main": [[{"node": "📤 En<PERSON><PERSON>pp - Consultor", "type": "main", "index": 0}]]}, "📝 Preparar Dados Proposta": {"main": [[{"node": "📤 En<PERSON><PERSON>pp - Consultor", "type": "main", "index": 0}]]}, "📝 Preparar Dados Proposta2": {"main": [[{"node": "📤 En<PERSON><PERSON>pp - Consultor", "type": "main", "index": 0}]]}}, "active": true, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "timezone": "UTC"}, "versionId": "02513fb1-06a0-44b7-b3f2-9226ea9e3274", "meta": {"templateCredsSetupCompleted": true, "instanceId": "7d9c52e678ad75e66f5e97a9edb1f2e4099de69ae1f15060ca3ce69b2f515b3f"}, "id": "d6YCd96YX8cNYsyH", "tags": []}