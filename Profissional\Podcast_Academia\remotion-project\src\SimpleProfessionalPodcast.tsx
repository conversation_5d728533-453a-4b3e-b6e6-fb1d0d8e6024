import React from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  staticFile,
  spring,
  Img,
} from "remotion";

interface SimpleProfessionalPodcastProps {
  videoPath: string;
  startTime: number;
  duration: number;
}

// Legendas simplificadas e focadas no essencial
const essentialSubtitles = [
  {
    text: "💰 Como conseguir",
    startTime: 0,
    endTime: 2.5,
    intensity: "high"
  },
  {
    text: "o primeiro cliente",
    startTime: 2.5,
    endTime: 5.2,
    intensity: "extreme"
  },
  {
    text: "e fechar um contrato",
    startTime: 5.2,
    endTime: 8.1,
    intensity: "high"
  },
  {
    text: "de R$ 25 mil",
    startTime: 8.1,
    endTime: 11.3,
    intensity: "extreme"
  },
  {
    text: "usando IA",
    startTime: 11.3,
    endTime: 14.2,
    intensity: "high"
  },
  {
    text: "em apenas 90 dias",
    startTime: 14.2,
    endTime: 17.5,
    intensity: "extreme"
  },
  {
    text: "sem experiência prévia",
    startTime: 17.5,
    endTime: 20.8,
    intensity: "medium"
  },
  {
    text: "Método comprovado",
    startTime: 20.8,
    endTime: 24.1,
    intensity: "high"
  },
  {
    text: "que funciona",
    startTime: 24.1,
    endTime: 27.2,
    intensity: "high"
  },
  {
    text: "para qualquer pessoa",
    startTime: 27.2,
    endTime: 30.5,
    intensity: "medium"
  },
  {
    text: "DESCUBRA O MÉTODO",
    startTime: 30.5,
    endTime: 34.2,
    intensity: "extreme"
  },
  {
    text: "AGORA MESMO!",
    startTime: 34.2,
    endTime: 37.8,
    intensity: "extreme"
  },
  {
    text: "👆 CLIQUE AQUI",
    startTime: 37.8,
    endTime: 42,
    intensity: "extreme"
  },
  {
    text: "Link na descrição",
    startTime: 42,
    endTime: 45,
    intensity: "high"
  }
];

export const SimpleProfessionalPodcast: React.FC<SimpleProfessionalPodcastProps> = ({
  videoPath,
  startTime,
  duration,
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps } = useVideoConfig();
  const time = frame / fps;

  // Encontrar legenda atual
  const currentSubtitle = essentialSubtitles.find(
    subtitle => time >= subtitle.startTime && time < subtitle.endTime
  );

  // Sistema de zoom sutil baseado na intensidade
  const getZoom = () => {
    if (!currentSubtitle) return 1.0;
    
    const zoomLevels = {
      extreme: 1.15,
      high: 1.08,
      medium: 1.03
    };
    
    const targetZoom = zoomLevels[currentSubtitle.intensity as keyof typeof zoomLevels] || 1.0;
    
    const zoomAnimation = spring({
      frame: frame - (currentSubtitle.startTime * fps),
      fps,
      config: { damping: 200, stiffness: 100 }
    });
    
    return 1.0 + (targetZoom - 1.0) * zoomAnimation;
  };

  // Sistema de shake muito sutil
  const getShake = () => {
    if (!currentSubtitle || currentSubtitle.intensity !== "extreme") {
      return { x: 0, y: 0 };
    }
    
    const shakeIntensity = 1.5;
    return {
      x: Math.sin(time * 8) * shakeIntensity,
      y: Math.cos(time * 6) * shakeIntensity * 0.5
    };
  };

  // Cores profissionais baseadas na intensidade
  const getColors = () => {
    if (!currentSubtitle) return { primary: "#FFD700", secondary: "#FFA500" };
    
    const colorSchemes = {
      extreme: { primary: "#FFD700", secondary: "#FF6B35" },
      high: { primary: "#FFD700", secondary: "#FFA500" },
      medium: { primary: "#FFA500", secondary: "#FFB347" }
    };
    
    return colorSchemes[currentSubtitle.intensity as keyof typeof colorSchemes];
  };

  const zoom = getZoom();
  const shake = getShake();
  const colors = getColors();

  return (
    <AbsoluteFill>
      {/* Vídeo principal com efeitos sutis */}
      <div
        style={{
          width: "100%",
          height: "100%",
          background: "#000",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          overflow: "hidden",
        }}
      >
        <Video
          src={staticFile(videoPath)}
          startFrom={startTime * fps}
          endAt={(startTime + duration) * fps}
          style={{
            width: "100%",
            height: "133.33%",
            objectFit: "cover",
            objectPosition: "center",
            transform: `
              scale(${zoom}) 
              translateX(${shake.x}px) 
              translateY(${shake.y}px)
            `,
            filter: currentSubtitle?.intensity === "extreme" 
              ? "brightness(1.05) contrast(1.1) saturate(1.2)"
              : "none",
            transition: "filter 0.3s ease",
          }}
          muted={true}
        />
      </div>

      {/* B-roll sutil apenas para momentos extremos */}
      {currentSubtitle?.intensity === "extreme" && currentSubtitle.text.includes("R$ 25 mil") && (
        <div
          style={{
            position: "absolute",
            top: "10%",
            right: "5%",
            width: "35%",
            height: "25%",
            borderRadius: "15px",
            overflow: "hidden",
            border: `3px solid ${colors.primary}`,
            boxShadow: `0 10px 30px rgba(0,0,0,0.6)`,
            opacity: interpolate(
              (time - currentSubtitle.startTime) / (currentSubtitle.endTime - currentSubtitle.startTime),
              [0, 0.2, 0.8, 1],
              [0, 1, 1, 0]
            ),
            zIndex: 10,
          }}
        >
          <Img
            src={staticFile("broll/money-stack-4k.jpg")}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
          />
        </div>
      )}

      {/* Sistema de legendas profissionais simplificado */}
      {currentSubtitle && (
        <ProfessionalSubtitle
          subtitle={currentSubtitle}
          time={time}
          colors={colors}
        />
      )}

      {/* Progress bar minimalista */}
      <div
        style={{
          position: "absolute",
          bottom: "15px",
          left: "20px",
          right: "20px",
          height: "4px",
          background: "rgba(255,255,255,0.2)",
          borderRadius: "2px",
          overflow: "hidden",
          zIndex: 25,
        }}
      >
        <div
          style={{
            width: `${(time / duration) * 100}%`,
            height: "100%",
            background: `linear-gradient(90deg, ${colors.primary}, ${colors.secondary})`,
            borderRadius: "2px",
            transition: "width 0.1s ease-out",
          }}
        />
      </div>
    </AbsoluteFill>
  );
};

// Componente de legenda profissional simplificado
const ProfessionalSubtitle: React.FC<{
  subtitle: any;
  time: number;
  colors: any;
}> = ({ subtitle, time, colors }) => {
  const progress = (time - subtitle.startTime) / (subtitle.endTime - subtitle.startTime);
  
  const scale = spring({
    frame: (time - subtitle.startTime) * 30,
    fps: 30,
    config: { damping: 200, stiffness: 150 }
  });

  const getFontSize = () => {
    const sizes = {
      extreme: "3.8rem",
      high: "3.2rem",
      medium: "2.8rem"
    };
    return sizes[subtitle.intensity as keyof typeof sizes] || "3.2rem";
  };

  const getContainerStyle = () => {
    const isExtreme = subtitle.intensity === "extreme";
    const isCTA = subtitle.text.includes("CLIQUE") || subtitle.text.includes("DESCUBRA");
    
    if (isCTA) {
      return {
        background: `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`,
        padding: "30px 45px",
        borderRadius: "25px",
        border: "3px solid #FFFFFF",
        boxShadow: `0 20px 50px rgba(0,0,0,0.8), 0 0 80px ${colors.primary}60`,
      };
    }
    
    return {
      background: isExtreme ? "rgba(0,0,0,0.9)" : "rgba(0,0,0,0.8)",
      padding: isExtreme ? "25px 35px" : "20px 30px",
      borderRadius: "20px",
      border: isExtreme ? `3px solid ${colors.primary}` : "2px solid rgba(255,255,255,0.4)",
      boxShadow: isExtreme 
        ? `0 15px 40px rgba(0,0,0,0.8), 0 0 60px ${colors.primary}40`
        : "0 10px 30px rgba(0,0,0,0.6)",
    };
  };

  return (
    <div
      style={{
        position: "absolute",
        bottom: subtitle.text.includes("CLIQUE") ? "50%" : "25%",
        left: "50%",
        transform: `
          translate(-50%, ${subtitle.text.includes("CLIQUE") ? "50%" : "0"}) 
          scale(${scale})
        `,
        textAlign: "center",
        zIndex: 30,
        opacity: interpolate(progress, [0, 0.1, 0.9, 1], [0, 1, 1, 0]),
        maxWidth: "90%",
      }}
    >
      <div style={getContainerStyle()}>
        <span
          style={{
            fontSize: getFontSize(),
            fontWeight: subtitle.intensity === "extreme" ? "900" : "800",
            color: "#FFFFFF",
            fontFamily: "'Inter', 'Helvetica Neue', -apple-system, sans-serif",
            textShadow: `
              0 4px 15px rgba(0,0,0,0.9),
              0 0 30px ${colors.primary}80
            `,
            lineHeight: "1.1",
            letterSpacing: subtitle.intensity === "extreme" ? "-0.02em" : "-0.01em",
            textTransform: subtitle.intensity === "extreme" ? "uppercase" : "none",
          }}
        >
          {subtitle.text}
        </span>
      </div>
    </div>
  );
};
