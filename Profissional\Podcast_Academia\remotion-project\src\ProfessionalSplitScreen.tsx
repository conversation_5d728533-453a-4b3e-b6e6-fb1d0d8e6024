import React, { useRef, useEffect, useState } from "react";
import {
  AbsoluteFill,
  Video,
  Audio,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  staticFile,
  Sequence,
  spring,
  Img,
  Easing,
} from "remotion";

interface ProfessionalSplitScreenProps {
  videoPath: string;
  audioPath: string;
  screenRecordingPath: string;
  startTime: number;
  duration: number;
}

// Sistema de legendas profissionais baseado no conteúdo real
const professionalLegends = [
  {
    id: "hook-opening",
    startTime: 0,
    endTime: 4.5,
    text: "Como construir seu primeiro Agente IA",
    type: "viral-hook",
    priority: "extreme",
    position: "center-bottom",
    style: "viral-highlight",
    effects: ["glow", "scale"]
  },
  {
    id: "timeline-promise",
    startTime: 4.5,
    endTime: 8.2,
    text: "do ZERO em 90 dias",
    type: "promise",
    priority: "extreme",
    position: "center-bottom",
    style: "promise-badge",
    effects: ["emphasis", "pulse"]
  },
  {
    id: "method-reveal",
    startTime: 8.2,
    endTime: 12.8,
    text: "Vou te mostrar o passo a passo completo",
    type: "method",
    priority: "high",
    position: "center-bottom",
    style: "method-box",
    effects: ["smooth-entrance"]
  },
  {
    id: "transformation",
    startTime: 12.8,
    endTime: 17.5,
    text: "Para transformar sua carreira com IA",
    type: "value-prop",
    priority: "high",
    position: "center-bottom",
    style: "value-highlight",
    effects: ["career-glow"]
  },
  {
    id: "foundation-step",
    startTime: 17.5,
    endTime: 22.1,
    text: "Primeiro: entender os fundamentos",
    type: "step",
    priority: "medium",
    position: "center-bottom",
    style: "step-indicator",
    effects: ["step-highlight"]
  },
  {
    id: "tools-step",
    startTime: 22.1,
    endTime: 26.8,
    text: "Segundo: escolher as ferramentas certas",
    type: "step",
    priority: "high",
    position: "center-bottom",
    style: "tools-focus",
    effects: ["tools-glow"]
  },
  {
    id: "practice-step",
    startTime: 26.8,
    endTime: 31.2,
    text: "Terceiro: colocar em prática",
    type: "action",
    priority: "extreme",
    position: "center-bottom",
    style: "action-call",
    effects: ["action-pulse"]
  },
  {
    id: "secret-reveal",
    startTime: 31.2,
    endTime: 35.5,
    text: "O segredo está na consistência",
    type: "secret",
    priority: "extreme",
    position: "center-bottom",
    style: "secret-reveal",
    effects: ["mystery-glow", "reveal-animation"]
  },
  {
    id: "proven-method",
    startTime: 35.5,
    endTime: 39.8,
    text: "Método comprovado",
    type: "authority",
    priority: "high",
    position: "center-bottom",
    style: "authority-badge",
    effects: ["authority-glow"]
  },
  {
    id: "social-proof",
    startTime: 39.8,
    endTime: 44.2,
    text: "Que funcionou para milhares de pessoas",
    type: "social-proof",
    priority: "extreme",
    position: "center-bottom",
    style: "social-validation",
    effects: ["social-glow", "numbers-emphasis"]
  }
];

// Sistema de B-roll contextual avançado
const contextualBrollSystem = {
  "viral-hook": { asset: "broll/ai-visualization.jpg", overlay: 0.7, timing: "immediate" },
  "promise": { asset: "broll/strategy-planning.jpg", overlay: 0.6, timing: "delayed" },
  "method": { asset: "broll/strategy-planning.jpg", overlay: 0.5, timing: "smooth" },
  "value-prop": { asset: "broll/success-celebration.jpg", overlay: 0.6, timing: "emphasis" },
  "step": { asset: "broll/ai-visualization.jpg", overlay: 0.4, timing: "subtle" },
  "action": { asset: "broll/handshake-professional.jpg", overlay: 0.8, timing: "immediate" },
  "secret": { asset: "broll/strategy-planning.jpg", overlay: 0.9, timing: "mystery" },
  "authority": { asset: "broll/contract-signing.jpg", overlay: 0.7, timing: "badge" },
  "social-proof": { asset: "broll/success-celebration.jpg", overlay: 0.8, timing: "community" }
};

export const ProfessionalSplitScreen: React.FC<ProfessionalSplitScreenProps> = ({
  videoPath,
  audioPath,
  screenRecordingPath,
  startTime,
  duration,
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps, width, height } = useVideoConfig();
  const time = frame / fps;

  // Sistema de análise de legendas atual
  const getCurrentLegend = () => {
    return professionalLegends.find(
      legend => time >= legend.startTime && time < legend.endTime
    );
  };

  const currentLegend = getCurrentLegend();
  
  // Sistema de timing para screen recording focus
  const getScreenFocus = () => {
    // Foco na tela durante explicações técnicas
    const screenFocusSegments = [
      { start: 17.5, end: 22.1 }, // Fundamentos
      { start: 22.1, end: 26.8 }, // Ferramentas
      { start: 26.8, end: 31.2 }, // Prática
    ];
    
    return screenFocusSegments.some(segment => 
      time >= segment.start && time <= segment.end
    );
  };

  const shouldFocusScreen = getScreenFocus();

  // Sistema de efeitos visuais profissionais
  const getVisualEffects = () => {
    if (!currentLegend) return { scale: 1, brightness: 1, contrast: 1 };

    const effects = currentLegend.effects || [];
    let visualConfig = { scale: 1, brightness: 1, contrast: 1, saturation: 1 };

    effects.forEach(effect => {
      switch(effect) {
        case "glow":
          visualConfig.brightness = 1 + (0.1 * Math.sin(time * 2));
          break;
        case "scale":
          visualConfig.scale = 1 + (0.02 * Math.sin(time * 3));
          break;
        case "emphasis":
          visualConfig.contrast = 1 + (0.1 * Math.sin(time * 4));
          break;
        case "pulse":
          visualConfig.saturation = 1 + (0.15 * Math.sin(time * 5));
          break;
      }
    });

    return visualConfig;
  };

  const effects = getVisualEffects();

  // Sistema de transições suaves
  const getTransitionEffect = () => {
    if (!currentLegend) return 1;
    
    const legendProgress = (time - currentLegend.startTime) / 
                          (currentLegend.endTime - currentLegend.startTime);
    
    return spring({
      frame: (legendProgress * fps * (currentLegend.endTime - currentLegend.startTime)),
      fps,
      config: { damping: 200, stiffness: 150 }
    });
  };

  return (
    <AbsoluteFill style={{ backgroundColor: "#000" }}>
      {/* Áudio principal sincronizado */}
      <Audio
        src={staticFile(audioPath)}
        startFrom={0}
        endAt={duration * fps}
        volume={1.0}
      />

      {/* LAYOUT SPLIT SCREEN PROFISSIONAL */}
      <div style={{
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        position: "relative"
      }}>
        
        {/* SEÇÃO SUPERIOR - INSTRUTOR/PESSOA (50%) */}
        <div style={{
          height: "50%",
          width: "100%",
          position: "relative",
          overflow: "hidden",
          background: "linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%)"
        }}>
          <Video
            src={staticFile(videoPath)}
            startFrom={startTime * fps}
            endAt={(startTime + duration) * fps}
            style={{
              width: "100%",
              height: "115%", // Crop para enquadramento melhor
              objectFit: "cover",
              objectPosition: "center 20%", // Foco no rosto
              transform: `scale(${effects.scale})`,
              filter: `
                brightness(${effects.brightness}) 
                contrast(${effects.contrast}) 
                saturate(${effects.saturation})
              `,
              transition: "all 0.2s ease"
            }}
            muted={true}
          />

          {/* Overlay profissional sutil */}
          <div style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: shouldFocusScreen 
              ? "linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.1) 100%)"
              : "linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.05) 100%)",
            transition: "background 0.5s ease"
          }} />

          {/* B-roll contextual estratégico */}
          {currentLegend?.type && contextualBrollSystem[currentLegend.type] && (
            <ContextualBrollOverlay
              brollConfig={contextualBrollSystem[currentLegend.type]}
              transitionEffect={getTransitionEffect()}
              priority={currentLegend.priority}
            />
          )}

          {/* Indicador de speaker ativo */}
          <div style={{
            position: "absolute",
            top: "15px",
            left: "15px",
            background: "rgba(34, 197, 94, 0.9)",
            padding: "6px 12px",
            borderRadius: "20px",
            display: "flex",
            alignItems: "center",
            gap: "6px",
            backdropFilter: "blur(10px)"
          }}>
            <div style={{
              width: "8px",
              height: "8px",
              borderRadius: "50%",
              background: "#fff",
              animation: "pulse 2s infinite"
            }} />
            <span style={{
              fontSize: "12px",
              fontWeight: "600",
              color: "#fff",
              fontFamily: "'SF Pro Display', sans-serif"
            }}>
              AO VIVO
            </span>
          </div>
        </div>

        {/* SEÇÃO INFERIOR - SCREEN RECORDING/FERRAMENTA (50%) */}
        <div style={{
          height: "50%",
          width: "100%",
          position: "relative",
          overflow: "hidden",
          background: "#000"
        }}>
          <Video
            src={staticFile(screenRecordingPath)}
            startFrom={startTime * fps}
            endAt={(startTime + duration) * fps}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              filter: shouldFocusScreen 
                ? "brightness(1.1) contrast(1.1) saturate(1.05)"
                : "brightness(0.95) contrast(0.95)",
              transition: "filter 0.5s ease",
              transform: shouldFocusScreen ? "scale(1.02)" : "scale(1)",
            }}
            muted={true}
          />

          {/* Overlay de foco quando necessário */}
          {shouldFocusScreen && (
            <div style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              border: "3px solid rgba(34, 197, 94, 0.6)",
              borderRadius: "8px",
              margin: "8px",
              pointerEvents: "none",
              boxShadow: "0 0 20px rgba(34, 197, 94, 0.3)"
            }} />
          )}

          {/* Indicador de foco na tela */}
          {shouldFocusScreen && (
            <div style={{
              position: "absolute",
              top: "15px",
              right: "15px",
              background: "rgba(34, 197, 94, 0.9)",
              padding: "6px 12px",
              borderRadius: "20px",
              backdropFilter: "blur(10px)"
            }}>
              <span style={{
                fontSize: "12px",
                fontWeight: "600",
                color: "#fff",
                fontFamily: "'SF Pro Display', sans-serif"
              }}>
                🎯 DEMONSTRAÇÃO
              </span>
            </div>
          )}
        </div>
      </div>

      {/* SISTEMA DE LEGENDAS PROFISSIONAIS */}
      {currentLegend && (
        <ProfessionalLegendRenderer
          legend={currentLegend}
          transitionEffect={getTransitionEffect()}
          screenFocus={shouldFocusScreen}
        />
      )}

      {/* SEPARADOR VISUAL ELEGANTE */}
      <div style={{
        position: "absolute",
        top: "50%",
        left: 0,
        right: 0,
        height: "1px",
        background: "linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%)",
        transform: "translateY(-0.5px)",
        zIndex: 10
      }} />

      {/* INDICADORES PROFISSIONAIS */}
      <ProfessionalIndicatorSystem
        currentLegend={currentLegend}
        progress={time / duration}
        totalLegends={professionalLegends.length}
        screenFocus={shouldFocusScreen}
      />
    </AbsoluteFill>
  );
};

// Componente de B-roll contextual
const ContextualBrollOverlay: React.FC<{
  brollConfig: any;
  transitionEffect: number;
  priority: string;
}> = ({ brollConfig, transitionEffect, priority }) => {
  const opacity = interpolate(
    transitionEffect,
    [0, 0.3, 0.7, 1],
    [0, brollConfig.overlay, brollConfig.overlay, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  const isHighPriority = priority === "extreme";

  return (
    <div style={{
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      opacity: isHighPriority ? opacity * 1.2 : opacity,
      zIndex: 3,
      transition: "opacity 0.3s ease"
    }}>
      <Img
        src={staticFile(brollConfig.asset)}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
          filter: "brightness(0.7) contrast(1.3) saturate(1.1)"
        }}
      />
      <div style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: isHighPriority 
          ? "rgba(34, 197, 94, 0.1)"
          : "rgba(0, 0, 0, 0.2)",
        mixBlendMode: "overlay"
      }} />
    </div>
  );
};

// Sistema de legendas profissionais
const ProfessionalLegendRenderer: React.FC<{
  legend: any;
  transitionEffect: number;
  screenFocus: boolean;
}> = ({ legend, transitionEffect, screenFocus }) => {
  const isExtremePriority = legend.priority === "extreme";
  const isViral = legend.type === "viral-hook" || legend.type === "secret";

  // Posicionamento estratégico baseado no foco
  const getPosition = () => {
    if (screenFocus) {
      return { bottom: "52%", zIndex: 200 }; // Acima da tela
    }
    return { bottom: "12%", zIndex: 100 }; // Posição padrão
  };

  const position = getPosition();

  return (
    <div style={{
      position: "absolute",
      left: "4%",
      right: "4%",
      ...position,
      opacity: transitionEffect,
      transform: `translateY(${(1 - transitionEffect) * 15}px)`,
      transition: "all 0.3s ease"
    }}>
      <div style={{
        background: isViral
          ? "linear-gradient(135deg, rgba(34, 197, 94, 0.95), rgba(22, 163, 74, 0.95))"
          : isExtremePriority
            ? "linear-gradient(135deg, rgba(59, 130, 246, 0.95), rgba(37, 99, 235, 0.95))"
            : "rgba(0, 0, 0, 0.9)",
        backdropFilter: "blur(20px)",
        border: isViral || isExtremePriority
          ? "2px solid rgba(255, 255, 255, 0.3)"
          : "1px solid rgba(255, 255, 255, 0.15)",
        borderRadius: "20px",
        padding: isExtremePriority ? "18px 28px" : "14px 24px",
        boxShadow: isViral
          ? "0 12px 40px rgba(34, 197, 94, 0.4)"
          : isExtremePriority
            ? "0 12px 40px rgba(59, 130, 246, 0.3)"
            : "0 8px 32px rgba(0, 0, 0, 0.6)"
      }}>
        <div style={{
          fontSize: isExtremePriority ? "22px" : "19px",
          fontWeight: isExtremePriority ? "800" : "700",
          color: "#ffffff",
          textAlign: "center",
          lineHeight: "1.3",
          fontFamily: "'SF Pro Display', -apple-system, sans-serif",
          textShadow: "0 2px 12px rgba(0,0,0,0.8)",
          letterSpacing: isExtremePriority ? "-0.5px" : "0"
        }}>
          {legend.text}
        </div>

        {/* Badge de prioridade */}
        {isViral && (
          <div style={{
            position: "absolute",
            top: "-10px",
            right: "20px",
            background: "rgba(255, 255, 255, 0.9)",
            color: "#16a34a",
            padding: "4px 12px",
            borderRadius: "12px",
            fontSize: "11px",
            fontWeight: "700",
            textTransform: "uppercase",
            letterSpacing: "0.5px"
          }}>
            🔥 VIRAL
          </div>
        )}
      </div>
    </div>
  );
};

// Sistema de indicadores profissionais
const ProfessionalIndicatorSystem: React.FC<{
  currentLegend: any;
  progress: number;
  totalLegends: number;
  screenFocus: boolean;
}> = ({ currentLegend, progress, totalLegends, screenFocus }) => {
  return (
    <>
      {/* Progress bar minimalista */}
      <div style={{
        position: "absolute",
        bottom: "6px",
        left: "16px",
        right: "16px",
        height: "3px",
        background: "rgba(255, 255, 255, 0.15)",
        borderRadius: "2px",
        overflow: "hidden",
        zIndex: 150
      }}>
        <div style={{
          height: "100%",
          background: currentLegend?.priority === "extreme"
            ? "linear-gradient(90deg, #22c55e, #16a34a)"
            : "linear-gradient(90deg, #6b7280, #374151)",
          width: `${progress * 100}%`,
          borderRadius: "2px",
          transition: "width 0.1s ease",
          boxShadow: "0 0 8px rgba(34, 197, 94, 0.5)"
        }} />
      </div>

      {/* Status indicator */}
      <div style={{
        position: "absolute",
        top: "15px",
        right: "15px",
        background: screenFocus 
          ? "rgba(34, 197, 94, 0.9)"
          : "rgba(0, 0, 0, 0.7)",
        backdropFilter: "blur(15px)",
        padding: "8px 16px",
        borderRadius: "16px",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        zIndex: 150
      }}>
        <div style={{
          fontSize: "12px",
          fontWeight: "600",
          color: "#fff",
          fontFamily: "'SF Pro Display', sans-serif",
          display: "flex",
          alignItems: "center",
          gap: "6px"
        }}>
          {screenFocus ? "🖥️ DEMO" : "🎤 CONCEITO"}
          <span style={{ opacity: 0.7 }}>
            {currentLegend ? 
              professionalLegends.findIndex(l => l.id === currentLegend.id) + 1 : 1
            }/{totalLegends}
          </span>
        </div>
      </div>

      {/* Tipo de conteúdo */}
      {currentLegend && (
        <div style={{
          position: "absolute",
          bottom: screenFocus ? "56%" : "26%",
          left: "16px",
          background: "rgba(0, 0, 0, 0.8)",
          backdropFilter: "blur(10px)",
          padding: "6px 12px",
          borderRadius: "12px",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          zIndex: 90
        }}>
          <span style={{
            fontSize: "11px",
            fontWeight: "600",
            color: "rgba(255, 255, 255, 0.8)",
            textTransform: "uppercase",
            letterSpacing: "0.5px"
          }}>
            {currentLegend.type.replace("-", " ")}
          </span>
        </div>
      )}
    </>
  );
};

export default ProfessionalSplitScreen;
