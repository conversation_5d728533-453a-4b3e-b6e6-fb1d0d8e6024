const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🎬 ADICIONANDO ÁUDIO COM FFMPEG - Solução Definitiva');
console.log('=' .repeat(60));

// Configurações
const videoFile = 'out/professional_test.mp4';
const audioFile = 'public/audio_extracted.mp3';
const outputFile = 'out/professional_final_with_audio.mp4';
const startTime = 729; // 12:09 - momento exato do primeiro cliente
const duration = 45; // 45 segundos

// Verificar se os arquivos existem
function checkFile(filePath, description) {
  if (!fs.existsSync(filePath)) {
    console.error(`❌ ${description} não encontrado: ${filePath}`);
    process.exit(1);
  }
  const stats = fs.statSync(filePath);
  console.log(`✅ ${description}: ${filePath} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
}

console.log('\n📁 VERIFICANDO ARQUIVOS:');
checkFile(videoFile, 'Vídeo base');
checkFile(audioFile, 'Arquivo de áudio');

console.log('\n🔧 CONFIGURAÇÕES:');
console.log(`📹 Vídeo: ${videoFile}`);
console.log(`🎵 Áudio: ${audioFile}`);
console.log(`⏰ Início: ${startTime}s (${Math.floor(startTime/60)}:${(startTime%60).toString().padStart(2,'0')})`);
console.log(`⏱️  Duração: ${duration}s`);
console.log(`💾 Saída: ${outputFile}`);

console.log('\n🚀 EXECUTANDO FFMPEG...');

try {
  // Comando FFmpeg otimizado para adicionar áudio sincronizado
  const ffmpegCommand = [
    'ffmpeg',
    '-y', // Sobrescrever arquivo de saída
    '-i', `"${videoFile}"`, // Vídeo de entrada
    '-ss', startTime.toString(), // Início do áudio
    '-t', duration.toString(), // Duração do áudio
    '-i', `"${audioFile}"`, // Áudio de entrada
    '-c:v', 'copy', // Copiar vídeo sem recodificar (mais rápido)
    '-c:a', 'aac', // Codec de áudio AAC
    '-b:a', '192k', // Bitrate de áudio 192k
    '-map', '0:v:0', // Mapear vídeo do primeiro input
    '-map', '1:a:0', // Mapear áudio do segundo input
    '-shortest', // Usar a duração do menor stream
    '-avoid_negative_ts', 'make_zero', // Evitar timestamps negativos
    `"${outputFile}"` // Arquivo de saída
  ].join(' ');

  console.log(`📝 Comando: ${ffmpegCommand}`);
  console.log('\n⏳ Processando...');

  const startTime_process = Date.now();
  
  // Executar comando
  execSync(ffmpegCommand, { 
    stdio: 'inherit',
    shell: true
  });

  const endTime = Date.now();
  const processingTime = Math.round((endTime - startTime_process) / 1000);

  console.log('\n✅ PROCESSAMENTO CONCLUÍDO!');
  console.log(`⏱️  Tempo de processamento: ${processingTime}s`);

  // Verificar arquivo de saída
  if (fs.existsSync(outputFile)) {
    const outputStats = fs.statSync(outputFile);
    console.log(`📁 Arquivo criado: ${outputFile}`);
    console.log(`📊 Tamanho: ${(outputStats.size / 1024 / 1024).toFixed(2)} MB`);
    
    console.log('\n🎯 VERIFICAÇÃO DE QUALIDADE:');
    
    // Verificar informações do vídeo final
    try {
      const probeCommand = `ffprobe -v quiet -print_format json -show_streams "${outputFile}"`;
      const probeResult = execSync(probeCommand, { encoding: 'utf8' });
      const streams = JSON.parse(probeResult).streams;
      
      const videoStream = streams.find(s => s.codec_type === 'video');
      const audioStream = streams.find(s => s.codec_type === 'audio');
      
      if (videoStream) {
        console.log(`✅ Vídeo: ${videoStream.codec_name} ${videoStream.width}x${videoStream.height}`);
        console.log(`   📊 Bitrate: ${Math.round(videoStream.bit_rate / 1000)}k`);
        console.log(`   🎬 FPS: ${eval(videoStream.r_frame_rate)}`);
      }
      
      if (audioStream) {
        console.log(`✅ Áudio: ${audioStream.codec_name} ${audioStream.sample_rate}Hz`);
        console.log(`   📊 Bitrate: ${Math.round(audioStream.bit_rate / 1000)}k`);
        console.log(`   🔊 Canais: ${audioStream.channels}`);
      } else {
        console.log('❌ Nenhum stream de áudio detectado!');
      }
      
    } catch (probeError) {
      console.log('⚠️  Não foi possível verificar streams (ffprobe não disponível)');
    }
    
    console.log('\n🎉 SUCESSO! Vídeo com áudio criado:');
    console.log(`📁 ${outputFile}`);
    console.log('\n💡 PRÓXIMOS PASSOS:');
    console.log('1. Reproduzir o vídeo para verificar sincronização');
    console.log('2. Ajustar timing se necessário');
    console.log('3. Usar este arquivo como base para versões finais');
    
  } else {
    console.error('❌ Arquivo de saída não foi criado!');
    process.exit(1);
  }

} catch (error) {
  console.error('\n❌ ERRO NO PROCESSAMENTO:');
  console.error(error.message);
  
  console.log('\n🔧 SOLUÇÕES POSSÍVEIS:');
  console.log('1. Verificar se FFmpeg está instalado: ffmpeg -version');
  console.log('2. Verificar se os arquivos de entrada existem');
  console.log('3. Verificar espaço em disco disponível');
  console.log('4. Tentar com parâmetros diferentes');
  
  process.exit(1);
}

console.log('\n✨ Script concluído!');
