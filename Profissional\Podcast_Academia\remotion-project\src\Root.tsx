import React from "react";
import { Composition } from "remotion";
import { InstagramReel } from "./InstagramReel";
import { InstagramStory } from "./InstagramStory";
import { InstagramPost } from "./InstagramPost";
import { ViralInstagramReel } from "./ViralInstagramReel";
import { ViralInstagramReelAdvanced } from "./ViralInstagramReelAdvanced";
import { ViralReelProfessional } from "./ViralReelProfessional";
import { ViralReelUltimate } from "./ViralReelUltimate";
import { VideoEditorInterface } from "./VideoEditorInterface";
import { ViralReelProfessionalUltimate } from "./ViralReelProfessionalUltimate";
import ProfessionalSplitScreen from "./ProfessionalSplitScreen";
import { SimpleProfessionalPodcast } from "./SimpleProfessionalPodcast";

export const RemotionRoot: React.FC = () => {
  return (
    <>
      {/* SIMPLE PROFESSIONAL PODCAST - Versão Simplificada e Focada */}
      <Composition
        id="SimpleProfessionalPodcast"
        component={SimpleProfessionalPodcast}
        durationInFrames={1350} // 45 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          startTime: 729, // 12:09 - momento exato do primeiro cliente
          duration: 45,
        }}
      />

      {/* SISTEMA PROFISSIONAL ULTRA-AVANÇADO - Split Screen */}
      <Composition
        id="ProfessionalSplitScreen"
        component={ProfessionalSplitScreen}
        durationInFrames={1320} // 44 segundos a 30fps - duração otimizada
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          audioPath: "audio_extracted.mp3",
          screenRecordingPath: "temp_video_base.mp4",
          startTime: 0, // Início do vídeo
          duration: 44,
        }}
      />

      {/* VIRAL REEL PROFESSIONAL ULTIMATE - Análise Avançada */}
      <Composition
        id="ViralReelProfessionalUltimate"
        component={ViralReelProfessionalUltimate}
        durationInFrames={1350} // 45 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          audioPath: "audio_extracted.mp3",
          startTime: 0, // Análise desde o início
          duration: 45,
          screenRecordingPath: "temp_video_base.mp4"
        }}
      />

      {/* COMPOSIÇÕES ORIGINAIS MANTIDAS */}
      <Composition
        id="InstagramReel"
        component={InstagramReel}
        durationInFrames={1800} // 60 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          startTime: 0,
          duration: 60,
          title: "Como construir seu primeiro Agente IA",
          subtitle: "Do ZERO em 90 dias",
          author: "Sami",
          colors: {
            primary: "#FF6B35",
            secondary: "#2E86AB",
            accent: "#A23B72",
            background: "#F18F01",
            text: "#FFFFFF"
          }
        }}
      />

      <Composition
        id="InstagramStory"
        component={InstagramStory}
        durationInFrames={450} // 15 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          startTime: 0,
          duration: 15,
          title: "Agente IA em 90 dias",
          author: "Sami",
          colors: {
            primary: "#22c55e",      // Verde profissional
            secondary: "#16a34a",    // Verde escuro
            accent: "#15803d",       // Verde accent
            background: "#f0fdf4",   // Verde suave
            text: "#111827"          // Preto para texto
          }
        }}
      />

      <Composition
        id="InstagramPost"
        component={InstagramPost}
        durationInFrames={1800} // 60 segundos a 30fps
        fps={30}
        width={1080}
        height={1080}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          startTime: 0,
          duration: 60,
          title: "Como construir seu primeiro Agente IA",
          author: "Sami",
          colors: {
            primary: "#FF6B35",
            secondary: "#2E86AB",
            accent: "#A23B72",
            background: "#F18F01",
            text: "#FFFFFF"
          }
        }}
      />

      {/* COMPOSIÇÕES VIRAIS AVANÇADAS */}
      <Composition
        id="ViralInstagramReel"
        component={ViralInstagramReel}
        durationInFrames={1350} // 45 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          audioPath: "audio_viral_segment.mp3",
          startTime: 720, // 12 minutos - momento do primeiro cliente
          duration: 45,
          title: "Como conseguir o primeiro cliente",
          subtitle: "E fechar um contrato de R$ 25 mil",
          hook: "💰 R$ 25 MIL em 90 dias",
          cta: "Descubra o método",
          author: "Academia"
        }}
      />

      <Composition
        id="ViralInstagramReelAdvanced"
        component={ViralInstagramReelAdvanced}
        durationInFrames={1350} // 45 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          audioPath: "audio_viral_segment.mp3",
          startTime: 720, // 12 minutos - momento do primeiro cliente
          duration: 45,
          title: "Como conseguir o primeiro cliente",
          subtitle: "E fechar um contrato de R$ 25 mil",
          hook: "💰 R$ 25 MIL em 90 dias",
          cta: "Descubra o método",
          author: "Academia"
        }}
      />

      <Composition
        id="ViralReelProfessional"
        component={ViralReelProfessional}
        durationInFrames={1350} // 45 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          audioPath: "audio_viral_segment.mp3",
          startTime: 729, // 12:09 - momento exato do primeiro cliente
          duration: 45,
        }}
      />

      <Composition
        id="ViralReelUltimate"
        component={ViralReelUltimate}
        durationInFrames={1350} // 45 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          audioPath: "audio_viral_segment.mp3",
          startTime: 729, // 12:09 - momento exato do primeiro cliente
          duration: 45,
        }}
      />

      {/* Interface de Edição Profissional */}
      <Composition
        id="VideoEditorInterface"
        component={VideoEditorInterface}
        durationInFrames={1350}
        fps={30}
        width={1920}
        height={1080}
        defaultProps={{}}
      />
    </>
  );
};
